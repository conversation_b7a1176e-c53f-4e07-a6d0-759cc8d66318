<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label="天气现象" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['weatherDescription', validatorRules.weatherDescription]"
                       placeholder="请输入天气现象"></a-input>
<!--              <j-dict-select-tag type="list" v-decorator="['weatherDescription', validatorRules.weatherDescription]"-->
<!--                                 :trigger-change="true" dictCode="weather_description" placeholder="请选择天气现象" />-->
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="能见度（km）" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number :controls="false" v-decorator="['visibility']" placeholder="请输入能见度（km）" style="width: 100%" />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="气压（mb）" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number :controls="false" v-decorator="['pressure', validatorRules.pressure]"
                       placeholder="请输入气压（mb）" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="降雨量（mm/hr）" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number :controls="false" v-decorator="['precipitation']" placeholder="请输入降雨量（mm/hr）" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="温度（℃）" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number :controls="false" :precision="1" :step="0.1" v-decorator="['temperature']" placeholder="请输入温度（℃）" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="体感温度（℃）" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number :controls="false" :precision="1" :step="0.1" v-decorator="['appTemp']" placeholder="请输入体感温度（℃）" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="紫外线指数" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number :controls="false" v-decorator="['ultravioletRays', validatorRules.ultravioletRays]"
                       placeholder="请输入紫外线指数" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="风向角度度数" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number :controls="false" v-decorator="['windDirection']" placeholder="请输入风向角度度数" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="风速（m/s）" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number :controls="false" :precision="1" :step="0.1" v-decorator="['windSpeed', validatorRules.windSpeed]"
                       placeholder="请输入风速（m/s）" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="数据更新时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date placeholder="请选择数据更新时间" v-decorator="['dateTime', validatorRules.dateTime]"
                      :trigger-change="true" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="风向描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['windMark']" placeholder="请输入风向描述"></a-input>
            </a-form-item>
          </a-col>
          <!-- 右列空白 -->
          <a-col :span="12">
          </a-col>

          <!-- 隐藏的其他字段，保持原有功能 -->
          <a-col :span="0" style="display: none;">
            <a-form-item label="小时" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['hour', validatorRules.hour]"
                       placeholder="请输入小时"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="0" style="display: none;">
            <a-form-item label="删除标识" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number autocomplete="off" v-decorator="['deleteFlag']" placeholder="请输入删除标识"
                              style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="0" style="display: none;">
            <a-form-item label="城市名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['cityName']" placeholder="请输入城市名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="0" style="display: none;">
            <a-form-item label="昼夜" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['pod']" placeholder="请输入昼夜"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="0" style="display: none;">
            <a-form-item label="天气代码" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['weatherCode']" placeholder="请输入天气代码"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="0" style="display: none;">
            <a-form-item label="云层量（%）" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['clouds']" placeholder="请输入云层量（%）"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="0" style="display: none;">
            <a-form-item label="降雪量（mm/hr）" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['snow']" placeholder="请输入降雪量（mm/hr）"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="0" style="display: none;">
            <a-form-item label="相对湿度（%）" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['rhumidity']" placeholder="请输入相对湿度（%）"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="0" style="display: none;">
            <a-form-item label="降雨概率" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['probability']" placeholder="请输入降雨概率"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="0" style="display: none;">
            <a-form-item label="seaPressure" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['seaPressure']" placeholder="请输入seaPressure"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="0" style="display: none;">
            <a-form-item label="snowDepth" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['snowDepth']" placeholder="请输入snowDepth"></a-input>
            </a-form-item>
          </a-col>

          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>

import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'

export default {
  name: 'SeaWeatherLiveForm',
  components: {},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {
      },
      required: false
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 8 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {
        hour: {
          rules: [
            { required: true, message: '请输入小时!' }
          ]
        },
        weatherDescription: {
          rules: [
            { required: true, message: '请输入天气现象!' }
          ]
        },
        pressure: {
          rules: [
            { required: true, message: '请输入气压!' }
          ]
        },
        ultravioletRays: {
          rules: [
            { required: true, message: '请输入紫外线指数!' }
          ]
        },
        windSpeed: {
          rules: [
            { required: true, message: '请输入风速!' }
          ]
        },
        dateTime: {
          rules: [
            { required: true, message: '请选择数据更新时间!' }
          ]
        }
      },
      url: {
        add: '/sea/seaWeatherLive/add',
        edit: '/sea/seaWeatherLive/edit',
        queryById: '/sea/seaWeatherLive/queryById'
      }
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    }
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'hour', 'deleteFlag', 'cityName', 'dateTime', 'weatherDescription', 'pod', 'visibility', 'weatherCode', 'clouds', 'pressure', 'precipitation', 'snow', 'temperature', 'appTemp', 'ultravioletRays', 'rhumidity', 'windDirection', 'windSpeed', 'windMark', 'probability', 'seaPressure', 'snowDepth'))
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
          })
        }

      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'hour', 'deleteFlag', 'cityName', 'dateTime', 'weatherDescription', 'pod', 'visibility', 'weatherCode', 'clouds', 'pressure', 'precipitation', 'snow', 'temperature', 'appTemp', 'ultravioletRays', 'rhumidity', 'windDirection', 'windSpeed', 'windMark', 'probability', 'seaPressure', 'snowDepth'))
    }
  }
}
</script>