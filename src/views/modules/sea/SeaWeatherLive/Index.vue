<template>
  <div class="list-page sea-weather-live-list">
    <table-layout
      :search-props="searchProps"
      :table-props="tableProps"
      @search-submit="searchSubmit"
      @table-change="searchSubmit"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>


</template>

<script>
import Modal from './components/Modal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import moment from 'dayjs'

export default {
  name: 'SeaWeatherLiveList',
  mixins: [JeecgListMixin],
  components: {
    Modal
  },
  data() {
    return {
      // props: {format:"YYYY-MM-DD" },
      // 搜索组件的props
      searchProps: {
        formModel: {
          dateTime: null
        },
        formItems: [
          {
            key: 'datetimerange',
            label: '数据更新时间',
            type: 'datetime_range',
            keyParams: ['startTime', 'endTime'],
            format: 'YYYY-MM-DD'
          }
        ]
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0
        },
        dataSource: [],
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            }
          },
          {
            title: '数据更新时间',
            align: 'center',
            dataIndex: 'createTime',
            width: 160,
            customRender: (t, r, index) => {
              return moment(t).format('YYYY-MM-DD HH') + ':00'
            }
          },
          {
            title: '天气现象',
            align: 'center',
            dataIndex: 'weatherDescription'
          },
          {
            title: '能见度（km）',
            align: 'center',
            dataIndex: 'visibility'
          },
          // {
          //   title: '云层量（%）',
          //   align: 'center',
          //   dataIndex: 'clouds',
          // },
          {
            title: '气压（mb）',
            align: 'center',
            dataIndex: 'pressure'
          },
          {
            title: '降雨量（mm/hr）',
            align: 'center',
            dataIndex: 'precipitation'
          },
          // {
          //   title: '降雪量（mm/hr）',
          //   align: 'center',
          //   dataIndex: 'snow',
          // },
          // {
          //   title: '浪高（m）',
          //   align: 'center',
          //   dataIndex: 'waveHeight',
          // },
          {
            title: '温度（℃）',
            align: 'center',
            dataIndex: 'temperature'
          },
          {
            title: '体感温度（℃）',
            align: 'center',
            dataIndex: 'appTemp'
          },
          {
            title: '紫外线指数',
            align: 'center',
            dataIndex: 'ultravioletRays'
          },
          // {
          //   title: '相对湿度（%）',
          //   align: 'center',
          //   dataIndex: 'rhumidity',
          // },
          {
            title: '风向角度度数',
            align: 'center',
            dataIndex: 'windDirection'
          },
          {
            title: '风速（m/s）',
            align: 'center',
            dataIndex: 'windSpeed'
          },
          {
            title: '风向描述',
            align: 'center',
            dataIndex: 'windMark'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' }
          }
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail
          },
          {
            text: '编辑',
            handler: this.handleEdit
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete
          }
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd
          }
        ]
      },

      url: {
        list: '/sea/seaWeatherLive/list',
        delete: '/sea/seaWeatherLive/delete',
        deleteBatch: '/sea/seaWeatherLive/deleteBatch',
        exportXlsUrl: '/sea/seaWeatherLive/exportXls',
        importExcelUrl: 'sea/seaWeatherLive/importExcel'
      }
    }
  },
  created() {
  },
  computed: {},
  watch: {},
  methods: {
    handleAdd() {
      this.$refs.modalForm.add()
      this.$refs.modalForm.title = '新增'
      this.$refs.modalForm.disableSubmit = false
    },
    searchSubmit(params) {
      const formModel = { ...params.formModel }
      delete formModel.datetimerange
      this.loadData(formModel)
    }
  }
}
</script>